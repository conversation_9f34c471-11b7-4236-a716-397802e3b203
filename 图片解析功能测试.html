<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片解析功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-case {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
            border-left: 4px solid #007bff;
        }
        .test-data {
            background: #e9ecef;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            color: #17a2b8;
            font-weight: bold;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🖼️ 图片解析功能测试</h1>
        
        <div class="test-case">
            <h3>测试用例 1: 标准图片数据格式</h3>
            <p>测试标准的 <code>choices[0].delta.images[]</code> 格式</p>
            <div class="test-data">
{
  "choices": [{
    "delta": {
      "images": [{
        "type": "image_url",
        "image_url": {
          "url": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        }
      }]
    }
  }]
}
            </div>
            <button onclick="testStandardFormat()">测试标准格式</button>
            <div id="result1" class="result" style="display: none;"></div>
        </div>

        <div class="test-case">
            <h3>测试用例 2: URL格式图片</h3>
            <p>测试直接URL格式的图片数据</p>
            <div class="test-data">
{
  "choices": [{
    "delta": {
      "images": [{
        "type": "image_url",
        "image_url": {
          "url": "https://via.placeholder.com/150x150.png?text=Test+Image"
        }
      }]
    }
  }]
}
            </div>
            <button onclick="testUrlFormat()">测试URL格式</button>
            <div id="result2" class="result" style="display: none;"></div>
        </div>

        <div class="test-case">
            <h3>测试用例 3: 多图片格式</h3>
            <p>测试同时包含多个图片的数据</p>
            <div class="test-data">
{
  "choices": [{
    "delta": {
      "images": [
        {
          "type": "image_url",
          "image_url": {
            "url": "https://via.placeholder.com/100x100.png?text=Image+1"
          }
        },
        {
          "type": "image_url", 
          "image_url": {
            "url": "https://via.placeholder.com/100x100.png?text=Image+2"
          }
        }
      ]
    }
  }]
}
            </div>
            <button onclick="testMultipleImages()">测试多图片</button>
            <div id="result3" class="result" style="display: none;"></div>
        </div>

        <div class="test-case">
            <h3>功能状态检查</h3>
            <p>检查图片解析功能的当前状态</p>
            <button onclick="checkFeatureStatus()">检查功能状态</button>
            <div id="result4" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 模拟图片解析功能的测试
        function testStandardFormat() {
            const result = document.getElementById('result1');
            result.style.display = 'block';
            
            try {
                const testData = {
                    choices: [{
                        delta: {
                            images: [{
                                type: "image_url",
                                image_url: {
                                    url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
                                }
                            }]
                        }
                    }]
                };
                
                // 模拟检测逻辑
                const delta = testData.choices[0].delta;
                if (delta.images && Array.isArray(delta.images)) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <span class="success">✅ 测试通过</span><br>
                        检测到 ${delta.images.length} 个图片<br>
                        图片类型: ${delta.images[0].type}<br>
                        数据格式: Base64 (${delta.images[0].image_url.url.substring(0, 50)}...)
                    `;
                } else {
                    throw new Error('未检测到图片数据');
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<span class="error">❌ 测试失败</span><br>错误: ${error.message}`;
            }
        }

        function testUrlFormat() {
            const result = document.getElementById('result2');
            result.style.display = 'block';
            
            try {
                const testData = {
                    choices: [{
                        delta: {
                            images: [{
                                type: "image_url",
                                image_url: {
                                    url: "https://via.placeholder.com/150x150.png?text=Test+Image"
                                }
                            }]
                        }
                    }]
                };
                
                const delta = testData.choices[0].delta;
                if (delta.images && Array.isArray(delta.images)) {
                    const url = delta.images[0].image_url.url;
                    const isUrl = url.startsWith('http://') || url.startsWith('https://');
                    
                    result.className = 'result success';
                    result.innerHTML = `
                        <span class="success">✅ 测试通过</span><br>
                        检测到 ${delta.images.length} 个图片<br>
                        图片类型: ${delta.images[0].type}<br>
                        数据格式: ${isUrl ? 'URL' : 'Base64'}<br>
                        图片地址: ${url}
                    `;
                } else {
                    throw new Error('未检测到图片数据');
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<span class="error">❌ 测试失败</span><br>错误: ${error.message}`;
            }
        }

        function testMultipleImages() {
            const result = document.getElementById('result3');
            result.style.display = 'block';
            
            try {
                const testData = {
                    choices: [{
                        delta: {
                            images: [
                                {
                                    type: "image_url",
                                    image_url: {
                                        url: "https://via.placeholder.com/100x100.png?text=Image+1"
                                    }
                                },
                                {
                                    type: "image_url",
                                    image_url: {
                                        url: "https://via.placeholder.com/100x100.png?text=Image+2"
                                    }
                                }
                            ]
                        }
                    }]
                };
                
                const delta = testData.choices[0].delta;
                if (delta.images && Array.isArray(delta.images)) {
                    result.className = 'result success';
                    result.innerHTML = `
                        <span class="success">✅ 测试通过</span><br>
                        检测到 ${delta.images.length} 个图片<br>
                        图片详情:<br>
                        ${delta.images.map((img, index) => 
                            `&nbsp;&nbsp;${index + 1}. ${img.type} - ${img.image_url.url}`
                        ).join('<br>')}
                    `;
                } else {
                    throw new Error('未检测到图片数据');
                }
            } catch (error) {
                result.className = 'result error';
                result.innerHTML = `<span class="error">❌ 测试失败</span><br>错误: ${error.message}`;
            }
        }

        function checkFeatureStatus() {
            const result = document.getElementById('result4');
            result.style.display = 'block';
            
            // 检查是否在聊天应用环境中
            if (typeof window.chatInstance !== 'undefined') {
                const isEnabled = window.chatInstance.settings.imageParsingEnabled;
                result.className = 'result success';
                result.innerHTML = `
                    <span class="info">ℹ️ 功能状态检查</span><br>
                    图片解析功能: ${isEnabled ? '<span class="success">已启用</span>' : '<span class="error">已禁用</span>'}<br>
                    聊天实例: 已连接<br>
                    建议: ${isEnabled ? '功能正常，可以开始测试' : '请在设置中启用图片解析功能'}
                `;
            } else {
                result.className = 'result error';
                result.innerHTML = `
                    <span class="error">⚠️ 环境检查</span><br>
                    聊天实例: 未连接<br>
                    状态: 当前不在聊天应用环境中<br>
                    建议: 请在主聊天页面中测试此功能
                `;
            }
        }
    </script>
</body>
</html>
