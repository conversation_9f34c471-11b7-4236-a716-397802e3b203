// 测试流式响应修复的JavaScript代码
// 在浏览器控制台中运行此代码来验证修复

console.log('🔧 开始测试流式响应修复...');

// 1. 检查图片解析功能状态
function checkImageParsingStatus() {
    if (typeof window.chatInstance !== 'undefined') {
        const isEnabled = window.chatInstance.settings.imageParsingEnabled;
        console.log('🖼️ 图片解析功能状态:', isEnabled ? '已启用' : '已禁用');
        return isEnabled;
    } else {
        console.error('❌ 聊天实例未找到');
        return false;
    }
}

// 2. 启用图片解析功能
function enableImageParsing() {
    if (typeof window.chatInstance !== 'undefined') {
        window.chatInstance.settings.imageParsingEnabled = true;
        window.chatInstance.saveSettings();
        
        // 更新UI
        const toggle = document.getElementById('imageParsingToggle');
        if (toggle) {
            toggle.checked = true;
        }
        
        console.log('✅ 图片解析功能已启用');
        return true;
    } else {
        console.error('❌ 聊天实例未找到');
        return false;
    }
}

// 3. 监控流式响应状态
function monitorStreamingStatus() {
    console.log('📡 开始监控流式响应状态...');
    
    // 监控发送按钮状态变化
    const sendBtn = document.getElementById('sendBtn');
    if (sendBtn) {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const btnText = sendBtn.textContent.trim();
                    const timestamp = new Date().toLocaleTimeString();
                    console.log(`📡 [${timestamp}] 发送按钮状态:`, btnText);
                }
            });
        });
        
        observer.observe(sendBtn, {
            childList: true,
            subtree: true,
            characterData: true
        });
        
        console.log('✅ 发送按钮状态监控已启动');
        return observer;
    } else {
        console.error('❌ 发送按钮未找到');
        return null;
    }
}

// 4. 检查流式响应处理代码
function checkStreamingCode() {
    console.log('🔍 检查流式响应处理代码...');
    
    if (typeof window.chatInstance !== 'undefined') {
        // 检查handleImageInResponse方法是否存在
        if (typeof window.chatInstance.handleImageInResponse === 'function') {
            console.log('✅ handleImageInResponse 方法存在');
        } else {
            console.error('❌ handleImageInResponse 方法不存在');
        }
        
        // 检查uploadBase64ImageToServer方法是否存在
        if (typeof window.chatInstance.uploadBase64ImageToServer === 'function') {
            console.log('✅ uploadBase64ImageToServer 方法存在');
        } else {
            console.error('❌ uploadBase64ImageToServer 方法不存在');
        }
        
        // 检查normalizeBase64Data方法是否存在
        if (typeof window.chatInstance.normalizeBase64Data === 'function') {
            console.log('✅ normalizeBase64Data 方法存在');
        } else {
            console.error('❌ normalizeBase64Data 方法不存在');
        }
        
        return true;
    } else {
        console.error('❌ 聊天实例未找到');
        return false;
    }
}

// 5. 模拟图片数据测试（非阻塞）
function testNonBlockingImageProcessing() {
    console.log('🧪 开始测试非阻塞图片处理...');
    
    if (typeof window.chatInstance === 'undefined') {
        console.error('❌ 聊天实例未找到');
        return;
    }

    // 模拟一个包含图片的AI回复数据
    const mockImageData = {
        type: "image_url",
        image_url: {
            url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        }
    };

    // 创建模拟的助手消息
    const mockAssistantMessage = {
        role: 'assistant',
        content: '这是一个测试图片：',
        images: [],
        timestamp: new Date().toISOString(),
        model_id: 'test-model'
    };

    // 创建模拟的内容元素
    const mockContentElement = document.createElement('div');
    mockContentElement.className = 'message-text';
    document.body.appendChild(mockContentElement);

    console.log('🖼️ 开始非阻塞图片处理测试...');
    const startTime = Date.now();
    
    // 调用图片处理方法（应该是非阻塞的）
    window.chatInstance.handleImageInResponse(mockImageData, mockAssistantMessage, mockContentElement)
        .then(() => {
            const endTime = Date.now();
            console.log(`✅ 图片处理完成，耗时: ${endTime - startTime}ms`);
            console.log('📝 消息内容:', mockAssistantMessage.content);
            console.log('🖼️ 图片数组:', mockAssistantMessage.images);
            
            // 清理测试元素
            document.body.removeChild(mockContentElement);
        })
        .catch(error => {
            console.error('❌ 图片处理失败:', error);
            document.body.removeChild(mockContentElement);
        });
    
    // 立即检查是否阻塞
    const immediateTime = Date.now();
    console.log(`⚡ 方法调用立即返回，耗时: ${immediateTime - startTime}ms`);
    
    if (immediateTime - startTime < 10) {
        console.log('✅ 图片处理是非阻塞的');
    } else {
        console.warn('⚠️ 图片处理可能是阻塞的');
    }
}

// 6. 完整测试流程
function runStreamingTest() {
    console.log('🚀 开始完整的流式响应测试...');
    
    // 步骤1: 检查功能状态
    const isEnabled = checkImageParsingStatus();
    
    // 步骤2: 如果未启用，则启用功能
    if (!isEnabled) {
        enableImageParsing();
    }
    
    // 步骤3: 检查代码
    checkStreamingCode();
    
    // 步骤4: 启动监控
    const observer = monitorStreamingStatus();
    
    // 步骤5: 测试非阻塞处理
    setTimeout(() => {
        testNonBlockingImageProcessing();
    }, 1000);
    
    console.log('📋 测试完成，请发送一个图片生成请求来验证修复效果');
    console.log('🔍 观察控制台日志和发送按钮状态变化');
    
    return observer;
}

// 导出测试函数到全局作用域
window.streamingTest = {
    checkStatus: checkImageParsingStatus,
    enable: enableImageParsing,
    monitor: monitorStreamingStatus,
    checkCode: checkStreamingCode,
    testNonBlocking: testNonBlockingImageProcessing,
    runFullTest: runStreamingTest
};

console.log('🔧 流式响应修复测试脚本已加载');
console.log('📋 可用的测试命令:');
console.log('  - streamingTest.checkStatus() - 检查功能状态');
console.log('  - streamingTest.enable() - 启用功能');
console.log('  - streamingTest.monitor() - 监控流式响应状态');
console.log('  - streamingTest.checkCode() - 检查代码完整性');
console.log('  - streamingTest.testNonBlocking() - 测试非阻塞处理');
console.log('  - streamingTest.runFullTest() - 运行完整测试');

// 自动运行基本检查
checkImageParsingStatus();
checkStreamingCode();
