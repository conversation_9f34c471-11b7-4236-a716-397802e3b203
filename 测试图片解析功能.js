// 测试图片解析功能的JavaScript代码
// 在浏览器控制台中运行此代码来测试功能

console.log('🖼️ 开始测试图片解析功能...');

// 1. 检查功能是否启用
function checkImageParsingEnabled() {
    if (typeof window.chatInstance !== 'undefined') {
        const isEnabled = window.chatInstance.settings.imageParsingEnabled;
        console.log('🖼️ 图片解析功能状态:', isEnabled ? '已启用' : '已禁用');
        return isEnabled;
    } else {
        console.error('❌ 聊天实例未找到');
        return false;
    }
}

// 2. 启用图片解析功能
function enableImageParsing() {
    if (typeof window.chatInstance !== 'undefined') {
        window.chatInstance.settings.imageParsingEnabled = true;
        window.chatInstance.saveSettings();
        
        // 更新UI
        const toggle = document.getElementById('imageParsingToggle');
        if (toggle) {
            toggle.checked = true;
        }
        
        console.log('✅ 图片解析功能已启用');
        return true;
    } else {
        console.error('❌ 聊天实例未找到');
        return false;
    }
}

// 3. 模拟图片数据流测试
function simulateImageResponse() {
    if (typeof window.chatInstance === 'undefined') {
        console.error('❌ 聊天实例未找到');
        return;
    }

    // 模拟一个包含图片的AI回复数据
    const mockImageData = {
        type: "image_url",
        image_url: {
            url: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
        }
    };

    // 创建模拟的助手消息
    const mockAssistantMessage = {
        role: 'assistant',
        content: '这是一个测试图片：',
        images: [],
        timestamp: new Date().toISOString(),
        model_id: 'test-model'
    };

    // 创建模拟的内容元素
    const mockContentElement = document.createElement('div');
    mockContentElement.className = 'message-text';
    document.body.appendChild(mockContentElement);

    console.log('🖼️ 开始模拟图片处理...');
    
    // 调用图片处理方法
    window.chatInstance.handleImageInResponse(mockImageData, mockAssistantMessage, mockContentElement)
        .then(() => {
            console.log('✅ 图片处理完成');
            console.log('📝 消息内容:', mockAssistantMessage.content);
            console.log('🖼️ 图片数组:', mockAssistantMessage.images);
            
            // 清理测试元素
            document.body.removeChild(mockContentElement);
        })
        .catch(error => {
            console.error('❌ 图片处理失败:', error);
            document.body.removeChild(mockContentElement);
        });
}

// 4. 测试Base64图片上传
function testBase64Upload() {
    if (typeof window.chatInstance === 'undefined') {
        console.error('❌ 聊天实例未找到');
        return;
    }

    // 一个1x1像素的透明PNG图片的Base64数据
    const testBase64 = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

    console.log('🖼️ 开始测试Base64图片上传...');
    
    window.chatInstance.uploadBase64ImageToServer(testBase64)
        .then(result => {
            console.log('✅ Base64图片上传成功:', result);
        })
        .catch(error => {
            console.error('❌ Base64图片上传失败:', error);
        });
}

// 5. 完整测试流程
function runFullTest() {
    console.log('🚀 开始完整测试流程...');
    
    // 步骤1: 检查功能状态
    const isEnabled = checkImageParsingEnabled();
    
    // 步骤2: 如果未启用，则启用功能
    if (!isEnabled) {
        enableImageParsing();
    }
    
    // 步骤3: 等待一秒后测试Base64上传
    setTimeout(() => {
        testBase64Upload();
    }, 1000);
    
    // 步骤4: 等待两秒后模拟图片响应
    setTimeout(() => {
        simulateImageResponse();
    }, 2000);
}

// 6. 检查设置界面
function checkSettingsUI() {
    const toggle = document.getElementById('imageParsingToggle');
    if (toggle) {
        console.log('✅ 图片解析开关找到，当前状态:', toggle.checked);
        return true;
    } else {
        console.error('❌ 图片解析开关未找到');
        return false;
    }
}

// 导出测试函数到全局作用域
window.imageParsingTest = {
    checkEnabled: checkImageParsingEnabled,
    enable: enableImageParsing,
    simulateResponse: simulateImageResponse,
    testUpload: testBase64Upload,
    runFullTest: runFullTest,
    checkUI: checkSettingsUI
};

console.log('🖼️ 图片解析功能测试脚本已加载');
console.log('📋 可用的测试命令:');
console.log('  - imageParsingTest.checkEnabled() - 检查功能状态');
console.log('  - imageParsingTest.enable() - 启用功能');
console.log('  - imageParsingTest.testUpload() - 测试图片上传');
console.log('  - imageParsingTest.simulateResponse() - 模拟图片响应');
console.log('  - imageParsingTest.runFullTest() - 运行完整测试');
console.log('  - imageParsingTest.checkUI() - 检查设置界面');

// 自动运行基本检查
checkImageParsingEnabled();
checkSettingsUI();
